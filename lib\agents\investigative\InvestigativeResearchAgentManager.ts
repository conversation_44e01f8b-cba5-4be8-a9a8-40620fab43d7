/**
 * Investigative Research Agent Manager
 * 
 * Manages the Investigative Research Agent system and integrates with PMO workflows.
 * Provides high-level interface for conducting investigations and managing results.
 */

import { InvestigativeResearchAgent, InvestigationRequest, InvestigationResult, InvestigationType, JournalistPersona } from './InvestigativeResearchAgent';
import { AgentMemoryManager } from '../AgentMemoryManager';
import { LlmProvider } from '../../tools/llm-tool';
import { processWithGroq } from '../../tools/groq-ai';
import { AgenticTeamId } from '../pmo/PMOInterfaces';
import { v4 as uuidv4 } from 'uuid';

export interface InvestigativeAgentManagerConfig {
  userId: string;
  defaultLlmProvider?: LlmProvider;
  defaultLlmModel?: string;
}

export interface InvestigationPreview {
  investigationType: InvestigationType;
  selectedJournalists: JournalistPersona[];
  estimatedDuration: string;
  expectedOutputs: string[];
  modelConfiguration: {
    criteriaModel: string;
    optimizationModel: string;
    assessmentModel: string;
    consolidationModel?: string;
  };
}

export interface PMOInvestigationRequest {
  pmoId: string;
  title: string;
  description: string;
  investigationType: InvestigationType;
  selectedJournalistIds?: string[]; // Optional - can use comparisonModels instead
  comparisonModels?: Array<{model: string, provider: LlmProvider}> | string[]; // LLM comparison models
  criteriaModel?: string;
  optimizationModel?: string;
  assessmentModel?: string;
  consolidate?: boolean;
  consolidationModel?: string;
  userId: string;
  priority: 'Low' | 'Medium' | 'High' | 'Critical';
  // Enhanced research capabilities
  enableInternetSearch?: boolean;
  enableDocumentAnalysis?: boolean;
  documentCategory?: string;
}

export interface InvestigationProgress {
  step: 'criteria' | 'optimization' | 'investigation' | 'assessment' | 'consolidation' | 'complete';
  percentage: number;
  currentActivity: string;
  estimatedTimeRemaining?: string;
}

/**
 * Investigative Research Agent Manager Class
 */
export class InvestigativeResearchAgentManager {
  private agent: InvestigativeResearchAgent;
  private memoryManager: AgentMemoryManager;
  private userId: string;
  private defaultLlmProvider: LlmProvider;
  private defaultLlmModel: string;

  constructor(config: InvestigativeAgentManagerConfig) {
    this.userId = config.userId;
    this.defaultLlmProvider = config.defaultLlmProvider || 'openai';
    this.defaultLlmModel = config.defaultLlmModel || 'gpt-o3-2025-04-16';
    
    this.agent = new InvestigativeResearchAgent(this.userId);
    this.memoryManager = new AgentMemoryManager(this.userId);
  }

  /**
   * Get available investigation types
   */
  getInvestigationTypes(): { type: InvestigationType; name: string; description: string }[] {
    return [
      {
        type: InvestigationType.INVESTIGATIVE,
        name: 'General Investigative',
        description: 'Deep-dive investigative reporting with focus on uncovering hidden truths'
      },
      {
        type: InvestigationType.FINANCIAL,
        name: 'Financial Investigation',
        description: 'Financial markets, corporate finance, and economic analysis'
      },
      {
        type: InvestigationType.POLITICAL,
        name: 'Political Investigation',
        description: 'Political systems, policy analysis, and government operations'
      },
      {
        type: InvestigationType.TECHNOLOGY,
        name: 'Technology Investigation',
        description: 'Technology trends, cybersecurity, and digital transformation'
      },
      {
        type: InvestigationType.SOCIAL_AFFAIRS,
        name: 'Social Affairs Investigation',
        description: 'Social issues, community impact, and human interest stories'
      },
      {
        type: InvestigationType.CORPORATE,
        name: 'Corporate Investigation',
        description: 'Corporate governance, business practices, and industry analysis'
      },
      {
        type: InvestigationType.ENVIRONMENTAL,
        name: 'Environmental Investigation',
        description: 'Environmental issues, climate change, and sustainability'
      },
      {
        type: InvestigationType.FEATURE,
        name: 'Feature Investigation',
        description: 'Long-form storytelling with narrative depth and comprehensive context'
      }
    ];
  }

  /**
   * Get available journalist personas
   */
  getAvailableJournalists(): JournalistPersona[] {
    return this.agent.getJournalistPersonas();
  }

  /**
   * Get recommended journalists for investigation type
   */
  getRecommendedJournalists(investigationType: InvestigationType): JournalistPersona[] {
    return this.agent.getJournalistsByType(investigationType);
  }

  /**
   * Use LLM to intelligently select the most appropriate journalists based on investigation content
   */
  async getIntelligentJournalistRecommendations(
    title: string,
    description: string,
    investigationType?: InvestigationType,
    maxJournalists: number = 3
  ): Promise<JournalistPersona[]> {
    try {
      // Get all available journalists
      const allJournalists = this.getAvailableJournalists();

      // Create journalist profiles for LLM analysis
      const journalistProfiles = allJournalists.map(j => ({
        id: j.id,
        name: j.name,
        specialty: j.specialty,
        description: j.description,
        expertise: j.expertise,
        investigationStyle: j.investigationStyle
      }));

      // Create LLM prompt for journalist selection
      const selectionPrompt = `
You are an expert investigative research coordinator tasked with selecting the most appropriate 
journalist personas for a specific investigation.

INVESTIGATION DETAILS:
Title: "${title}"
Description: "${description}"
${investigationType ? `Suggested Type: ${investigationType}` : ''}

AVAILABLE JOURNALIST PERSONAS:
${journalistProfiles.map((j, index) => `
${index + 1}. ${j.name} (ID: ${j.id})
   - Specialty: ${j.specialty}
   - Description: ${j.description}
   - Expertise: ${j.expertise.join(', ')}
   - Investigation Style: ${j.investigationStyle}
`).join('')}

SELECTION CRITERIA:
1. Relevance to investigation topic and scope
2. Complementary expertise and perspectives
3. Investigation style alignment with requirements
4. Coverage of different aspects (financial, technical, social, etc.)
5. Professional journalism standards and methodologies

TASK:
Select the ${maxJournalists} most appropriate journalist personas for this investigation. Consider:
- Which journalists have the most relevant expertise for this specific investigation
- How their different perspectives will provide comprehensive coverage
- Which combination will deliver the highest quality investigative analysis

RESPONSE FORMAT:
Return only a JSON array of journalist IDs in order of priority:
["journalist-id-1", "journalist-id-2", "journalist-id-3"]

Do not include any explanation or additional text - only the JSON array.
`;

      // Use Groq DeepSeek to intelligently select journalists
      const response = await processWithGroq({
        prompt: selectionPrompt,
        model: "deepseek-r1-distill-llama-70b", // Use DeepSeek model via Groq
        modelOptions: {
          temperature: 0.3, // Lower temperature for more consistent selection
          maxTokens: 2000
        }
      });

      // Parse LLM response
      let selectedIds: string[] = [];
      try {
        const cleanResponse = response.trim().replace(/```json\n?|\n?```/g, '');
        selectedIds = JSON.parse(cleanResponse);
      } catch (parseError) {
        console.warn('Failed to parse LLM journalist selection, using fallback:', parseError);
        // Fallback to type-based selection
        const fallbackJournalists = investigationType
          ? this.getRecommendedJournalists(investigationType)
          : allJournalists.slice(0, maxJournalists);
        return fallbackJournalists.slice(0, maxJournalists);
      }

      // Validate and get journalist personas
      const selectedJournalists = selectedIds
        .map(id => this.agent.getJournalistPersona(id))
        .filter(Boolean) as JournalistPersona[];

      // Ensure we have at least one journalist
      if (selectedJournalists.length === 0) {
        console.warn('LLM selection returned no valid journalists, using fallback');
        const fallbackJournalists = investigationType
          ? this.getRecommendedJournalists(investigationType)
          : allJournalists.slice(0, maxJournalists);
        return fallbackJournalists.slice(0, maxJournalists);
      }

      console.log(`LLM selected ${selectedJournalists.length} journalists:`, selectedJournalists.map(j => j.name));
      return selectedJournalists.slice(0, maxJournalists);

    } catch (error) {
      console.error('Error in intelligent journalist selection:', error);
      // Fallback to type-based selection
      const fallbackJournalists = investigationType
        ? this.getRecommendedJournalists(investigationType)
        : this.getAvailableJournalists().slice(0, maxJournalists);
      return fallbackJournalists.slice(0, maxJournalists);
    }
  }

  /**
   * Preview investigation configuration
   */
  previewInvestigation(request: Partial<PMOInvestigationRequest>): InvestigationPreview {
    const investigationType = request.investigationType || InvestigationType.INVESTIGATIVE;
    const selectedJournalistIds = request.selectedJournalistIds || [];
    
    const selectedJournalists = selectedJournalistIds
      .map(id => this.agent.getJournalistPersona(id))
      .filter(Boolean) as JournalistPersona[];

    // If no journalists selected, use recommended ones
    if (selectedJournalists.length === 0) {
      selectedJournalists.push(...this.getRecommendedJournalists(investigationType).slice(0, 3));
    }

    const estimatedDuration = this.calculateEstimatedDuration(selectedJournalists.length, request.consolidate);
    const expectedOutputs = this.getExpectedOutputs(investigationType, request.consolidate);

    return {
      investigationType,
      selectedJournalists,
      estimatedDuration,
      expectedOutputs,
      modelConfiguration: {
        criteriaModel: request.criteriaModel || 'gemini-2.5-pro',
        optimizationModel: request.optimizationModel || 'gpt-4o',
        assessmentModel: request.assessmentModel || 'claude-sonnet-4-0',
        consolidationModel: request.consolidate ? (request.consolidationModel || 'o3-2025-04-16') : undefined
      }
    };
  }

  /**
   * Conduct investigation from PMO request
   */
  async conductPMOInvestigation(
    request: PMOInvestigationRequest,
    onProgress?: (progress: InvestigationProgress) => void
  ): Promise<InvestigationResult> {
    console.log(`Starting PMO investigation for ${request.pmoId}: ${request.title}`);

    // Convert PMO request to investigation request
    const investigationRequest: InvestigationRequest = {
      prompt: request.title,
      context: request.description,
      investigationType: request.investigationType,
      selectedJournalists: request.selectedJournalistIds,
      comparisonModels: request.comparisonModels,
      criteriaModel: request.criteriaModel,
      optimizationModel: request.optimizationModel,
      assessmentModel: request.assessmentModel,
      consolidate: request.consolidate || false,
      consolidationModel: request.consolidationModel,
      userId: request.userId,
      pmoId: request.pmoId,
      // Enhanced research capabilities
      enableInternetSearch: request.enableInternetSearch !== false, // Default to true
      enableDocumentAnalysis: request.enableDocumentAnalysis !== false, // Default to true
      documentCategory: request.documentCategory
    };

    // Set up progress tracking
    const progressCallback = (step: string, percentage: number, activity: string) => {
      if (onProgress) {
        onProgress({
          step: step as any,
          percentage,
          currentActivity: activity,
          estimatedTimeRemaining: this.calculateRemainingTime(percentage)
        });
      }
    };

    try {
      // Step 1: Criteria generation
      progressCallback('criteria', 10, 'Generating investigation criteria...');
      
      // Step 2: Prompt optimization
      progressCallback('optimization', 25, 'Optimizing investigation prompt...');
      
      // Step 3: Investigation
      progressCallback('investigation', 40, `Conducting investigation with ${investigationRequest.selectedJournalists.length} journalists...`);
      
      // Step 4: Assessment
      progressCallback('assessment', 75, 'Assessing and comparing journalist findings...');
      
      // Step 5: Optional consolidation
      if (investigationRequest.consolidate) {
        progressCallback('consolidation', 90, 'Consolidating investigation findings...');
      }

      // Conduct the actual investigation
      const result = await this.agent.conductInvestigation(investigationRequest);

      // Complete
      progressCallback('complete', 100, 'Investigation completed successfully');

      // Store PMO-specific metadata
      await this.storePMOInvestigationResult(result, request);

      // Store investigation result in Firebase Agent_Output collection for PMO Output tab
      await this.storePMOAgentOutput(result, request);

      return result;

    } catch (error) {
      console.error(`PMO investigation failed for ${request.pmoId}:`, error);
      throw error;
    }
  }

  /**
   * Get investigation history for PMO
   */
  async getPMOInvestigationHistory(pmoId?: string, limit: number = 10): Promise<any[]> {
    try {
      const allHistory = await this.agent.getInvestigationHistory(this.userId, limit * 2);
      
      if (pmoId) {
        return allHistory.filter(item => item.metadata?.pmoId === pmoId).slice(0, limit);
      }
      
      return allHistory.slice(0, limit);
    } catch (error) {
      console.error('Failed to retrieve PMO investigation history:', error);
      return [];
    }
  }

  /**
   * Calculate estimated duration based on configuration
   */
  private calculateEstimatedDuration(journalistCount: number, consolidate?: boolean): string {
    const baseTime = 3; // 3 minutes base
    const journalistTime = journalistCount * 1.5; // 1.5 minutes per journalist
    const consolidationTime = consolidate ? 2 : 0; // 2 minutes for consolidation
    
    const totalMinutes = baseTime + journalistTime + consolidationTime;
    
    if (totalMinutes < 60) {
      return `${Math.round(totalMinutes)} minutes`;
    } else {
      const hours = Math.floor(totalMinutes / 60);
      const minutes = Math.round(totalMinutes % 60);
      return `${hours}h ${minutes}m`;
    }
  }

  /**
   * Get expected outputs for investigation type
   */
  private getExpectedOutputs(investigationType: InvestigationType, consolidate?: boolean): string[] {
    const baseOutputs = [
      'Investigation criteria and methodology',
      'Optimized research question',
      'Individual journalist investigations',
      'Comparative analysis and assessment'
    ];

    if (consolidate) {
      baseOutputs.push('Consolidated investigation report');
    }

    baseOutputs.push('Key findings and recommendations', 'Source references and citations');

    // Add investigation-specific outputs
    switch (investigationType) {
      case InvestigationType.FINANCIAL:
        baseOutputs.push('Financial analysis and market implications');
        break;
      case InvestigationType.POLITICAL:
        baseOutputs.push('Policy implications and political context');
        break;
      case InvestigationType.TECHNOLOGY:
        baseOutputs.push('Technical analysis and innovation insights');
        break;
      case InvestigationType.SOCIAL_AFFAIRS:
        baseOutputs.push('Community impact assessment');
        break;
    }

    return baseOutputs;
  }

  /**
   * Calculate remaining time based on progress percentage
   */
  private calculateRemainingTime(percentage: number): string {
    if (percentage >= 100) return '0 minutes';
    
    const totalEstimatedMinutes = 8; // Average investigation time
    const remainingMinutes = Math.round((100 - percentage) / 100 * totalEstimatedMinutes);
    
    if (remainingMinutes <= 1) return 'Less than 1 minute';
    return `${remainingMinutes} minutes`;
  }

  /**
   * Store PMO-specific investigation result metadata
   */
  private async storePMOInvestigationResult(result: InvestigationResult, request: PMOInvestigationRequest): Promise<void> {
    try {
      // Store in agent memory using the correct AgentMemory format
      const agentMemory = {
        Agent_Response: {
          [`pmo_investigation_${result.investigationId}`]: {
            investigationId: result.investigationId,
            pmoId: request.pmoId,
            title: request.title,
            investigationType: request.investigationType,
            priority: request.priority,
            journalistCount: result.journalistResponses.length,
            hasConsolidation: !!result.consolidatedReport,
            keyFindings: result.keyFindings.slice(0, 5), // Store top 5 findings
            completedAt: new Date().toISOString(),
            teamId: AgenticTeamId.InvestigativeResearch, // Associate with Investigative Research team
            modelConfiguration: {
              criteriaModel: result.criteriaModel,
              optimizationModel: result.optimizationModel,
              assessmentModel: result.assessmentModel,
              consolidationModel: result.consolidationModel
            }
          }
        }
      };

      await this.memoryManager.saveMemory('investigative-research-agent-manager', agentMemory);

      console.log(`PMO investigation metadata stored for ${request.pmoId}`);
    } catch (error) {
      console.error('Failed to store PMO investigation metadata:', error);
    }
  }

  /**
   * Store investigation result in Firebase Agent_Output collection for PMO Output tab visibility
   * Creates separate documents for each journalist report and assessment results
   */
  private async storePMOAgentOutput(result: InvestigationResult, request: PMOInvestigationRequest): Promise<void> {
    try {
      console.log(`[INVESTIGATIVE_AGENT_OUTPUT] *** STARTING STORAGE *** for PMO ${request.pmoId} with ${result.journalistResponses.length} journalist reports`);

      // Import Firebase admin here to avoid circular dependencies
      const { adminDb } = await import('../../../components/firebase-admin');
      console.log(`[INVESTIGATIVE_AGENT_OUTPUT] Firebase admin imported successfully`);

      // Use PMO ID as the investigation ID for consistency
      const investigationId = request.pmoId;
      const investigationTimestamp = result.createdAt; // Use actual investigation completion time

      console.log(`[INVESTIGATIVE_AGENT_OUTPUT] Investigation ID: ${investigationId}, Timestamp: ${investigationTimestamp}`);

      // Store individual journalist reports as separate Agent_Output documents
      for (let i = 0; i < result.journalistResponses.length; i++) {
        const journalistResponse = result.journalistResponses[i];
        const journalistRequestId = `${investigationId}_journalist_${i + 1}`;

        const journalistOutputData = {
          requestId: journalistRequestId,
          timestamp: investigationTimestamp,
          agentType: 'Investigative Research - Journalist Report',
          userId: this.userId,
          prompt: `${request.title} - ${journalistResponse.journalistName} Investigation`,
          result: {
            thinking: `Investigation approach: ${journalistResponse.investigationAngle}`,
            output: journalistResponse.response,
            documentUrl: null
          },
          agentMessages: [],
          modelInfo: {
            provider: journalistResponse.provider,
            model: journalistResponse.model
          },
          // PMO-specific metadata
          pmoMetadata: {
            pmoId: request.pmoId,
            investigationType: request.investigationType,
            priority: request.priority,
            journalistName: journalistResponse.journalistName,
            journalistId: journalistResponse.journalistId,
            investigationAngle: journalistResponse.investigationAngle,
            keyFindings: journalistResponse.keyFindings || [],
            reportNumber: i + 1,
            totalReports: result.journalistResponses.length
          },
          category: `PMO - ${request.title} - ${request.pmoId} - ${journalistResponse.journalistName}`,
          contextOptions: {
            investigationId: investigationId,
            teamId: AgenticTeamId.InvestigativeResearch,
            teamName: 'Investigative Research',
            reportType: 'journalist_report',
            journalistName: journalistResponse.journalistName
          }
        };

        const cleanedJournalistData = this.removeUndefinedValues(journalistOutputData);
        console.log(`[INVESTIGATIVE_AGENT_OUTPUT] About to store journalist document with ID: ${journalistRequestId}`);
        await adminDb.collection('Agent_Output').doc(journalistRequestId).set(cleanedJournalistData);
        console.log(`[INVESTIGATIVE_AGENT_OUTPUT] ✅ Successfully stored journalist report ${i + 1}: ${journalistResponse.journalistName}`);
      }

      // Store assessment results as a separate Agent_Output document
      const assessmentRequestId = `${investigationId}_assessment`;

      // Extract percentage scores and reasoning from assessment
      const assessmentScores = this.extractAssessmentScores(result.assessment);
      const selectedReportReasoning = this.extractSelectedReportReasoning(result.assessment);

      const assessmentOutputData = {
        requestId: assessmentRequestId,
        timestamp: investigationTimestamp,
        agentType: 'Investigative Research - Assessment',
        userId: this.userId,
        prompt: `${request.title} - Investigation Assessment & Comparison`,
        result: {
          thinking: 'Comparative analysis of journalist investigations using multi-LLM assessment methodology',
          output: result.assessment,
          documentUrl: null
        },
        agentMessages: [],
        modelInfo: {
          provider: result.assessmentProvider,
          model: result.assessmentModel
        },
        // PMO-specific metadata
        pmoMetadata: {
          pmoId: request.pmoId,
          investigationType: request.investigationType,
          priority: request.priority,
          assessmentScores: assessmentScores,
          selectedReportReasoning: selectedReportReasoning,
          consolidatedReport: result.consolidatedReport,
          hasConsolidation: !!result.consolidatedReport,
          keyFindings: result.keyFindings.slice(0, 5),
          recommendations: result.recommendations.slice(0, 3),
          totalJournalistReports: result.journalistResponses.length
        },
        category: `PMO - ${request.title} - ${request.pmoId} - Assessment`,
        contextOptions: {
          investigationId: investigationId,
          teamId: AgenticTeamId.InvestigativeResearch,
          teamName: 'Investigative Research',
          reportType: 'assessment_report'
        }
      };

      const cleanedAssessmentData = this.removeUndefinedValues(assessmentOutputData);
      console.log(`[INVESTIGATIVE_AGENT_OUTPUT] About to store assessment document with ID: ${assessmentRequestId}`);
      await adminDb.collection('Agent_Output').doc(assessmentRequestId).set(cleanedAssessmentData);
      console.log(`[INVESTIGATIVE_AGENT_OUTPUT] ✅ Successfully stored assessment report for PMO ${request.pmoId}`);

      console.log(`[INVESTIGATIVE_AGENT_OUTPUT] *** STORAGE COMPLETE *** Successfully stored ${result.journalistResponses.length + 1} investigation documents for PMO ${request.pmoId}`);

    } catch (storageError) {
      console.error(`[INVESTIGATIVE_AGENT_OUTPUT] ❌ ERROR storing investigation output:`, storageError);
      console.error(`[INVESTIGATIVE_AGENT_OUTPUT] Error details:`, {
        message: storageError.message,
        stack: storageError.stack,
        pmoId: request.pmoId,
        investigationId: request.pmoId
      });
      // Continue with the response even if storage fails
    }
  }

  /**
   * Format investigation result content for PMO Output display
   */
  private formatInvestigationContent(result: InvestigationResult): string {
    let content = `# Investigation Report\n\n`;
    content += `**Investigation ID:** ${result.investigationId}\n`;
    content += `**Journalists:** ${result.journalistResponses.length}\n`;
    content += `**Models Used:** Criteria: ${result.criteriaModel}, Optimization: ${result.optimizationModel}, Assessment: ${result.assessmentModel}\n\n`;

    // Key Findings
    if (result.keyFindings && result.keyFindings.length > 0) {
      content += `## Key Findings\n\n`;
      result.keyFindings.slice(0, 5).forEach((finding, index) => {
        content += `${index + 1}. ${finding}\n`;
      });
      content += `\n`;
    }

    // Recommendations
    if (result.recommendations && result.recommendations.length > 0) {
      content += `## Recommendations\n\n`;
      result.recommendations.slice(0, 3).forEach((rec, index) => {
        content += `${index + 1}. ${rec}\n`;
      });
      content += `\n`;
    }

    // Consolidated Report (if available)
    if (result.consolidatedReport) {
      content += `## Consolidated Analysis\n\n`;
      content += result.consolidatedReport.substring(0, 1000); // Limit to first 1000 chars
      if (result.consolidatedReport.length > 1000) {
        content += `...\n\n*[Report truncated for display]*`;
      }
    } else {
      // Show journalist responses summary
      content += `## Journalist Responses Summary\n\n`;
      result.journalistResponses.forEach((response) => {
        if (response.response) {
          content += `### ${response.journalistName}\n`;
          content += `**Investigation Angle:** ${response.investigationAngle}\n\n`;
          content += response.response.substring(0, 300); // First 300 chars
          if (response.response.length > 300) {
            content += `...\n`;
          }
          content += `\n\n`;
        }
      });
    }

    return content;
  }

  /**
   * Extract assessment scores from the assessment text
   */
  private extractAssessmentScores(assessment: string): Array<{reportNumber: number, score: number, reasoning: string}> {
    const scores: Array<{reportNumber: number, score: number, reasoning: string}> = [];

    try {
      // Look for patterns like "Report 1: 85%" or "Model 1 (85%)" or "Score: 8.5/10"
      const scorePatterns = [
        /(?:Report|Model)\s*(\d+).*?(\d+)%/gi,
        /(?:Report|Model)\s*(\d+).*?(\d+(?:\.\d+)?)\s*\/\s*10/gi,
        /(\d+)(?:st|nd|rd|th)?\s*(?:report|model).*?(\d+)%/gi
      ];

      let reportNumber = 1;
      const lines = assessment.split('\n');

      for (const line of lines) {
        for (const pattern of scorePatterns) {
          const matches = [...line.matchAll(pattern)];
          for (const match of matches) {
            const num = parseInt(match[1]);
            let score = parseFloat(match[2]);

            // Convert /10 scores to percentages
            if (line.includes('/10')) {
              score = score * 10;
            }

            scores.push({
              reportNumber: num,
              score: score,
              reasoning: line.trim()
            });
          }
        }
      }

      // If no specific scores found, create placeholder scores
      if (scores.length === 0) {
        const reportCount = (assessment.match(/(?:Report|Model)\s*\d+/gi) || []).length;
        for (let i = 1; i <= Math.max(reportCount, 3); i++) {
          scores.push({
            reportNumber: i,
            score: 0, // Placeholder score
            reasoning: `Assessment for Report ${i} - see full assessment for details`
          });
        }
      }

    } catch (error) {
      console.error('Error extracting assessment scores:', error);
    }

    return scores;
  }

  /**
   * Extract the reasoning for which report was selected as best
   */
  private extractSelectedReportReasoning(assessment: string): string {
    try {
      // Look for patterns indicating the best/selected report
      const reasoningPatterns = [
        /(?:best|selected|recommended|winner|top).*?(?:report|model).*?(?:because|due to|as it|since)/gi,
        /(?:report|model)\s*\d+.*?(?:is the best|stands out|excels|superior)/gi,
        /overall.*?(?:report|model)\s*\d+/gi
      ];

      for (const pattern of reasoningPatterns) {
        const match = assessment.match(pattern);
        if (match) {
          // Extract the paragraph containing this match
          const sentences = assessment.split(/[.!?]+/);
          for (const sentence of sentences) {
            if (sentence.toLowerCase().includes(match[0].toLowerCase())) {
              return sentence.trim() + '.';
            }
          }
        }
      }

      // Fallback: look for conclusion section
      const conclusionMatch = assessment.match(/(?:conclusion|final|overall|summary)[\s\S]*?(?:\n\n|\n#|$)/i);
      if (conclusionMatch) {
        return conclusionMatch[0].trim();
      }

      return 'See full assessment for detailed reasoning on report selection.';
    } catch (error) {
      console.error('Error extracting selected report reasoning:', error);
      return 'Assessment reasoning extraction failed.';
    }
  }

  /**
   * Remove undefined values from object (Firestore doesn't allow undefined)
   */
  private removeUndefinedValues(obj: any): any {
    if (obj === null || typeof obj !== 'object') {
      return obj;
    }

    if (Array.isArray(obj)) {
      return obj.map(item => this.removeUndefinedValues(item));
    }

    const cleaned: any = {};
    for (const [key, value] of Object.entries(obj)) {
      if (value !== undefined) {
        cleaned[key] = this.removeUndefinedValues(value);
      }
    }
    return cleaned;
  }
}
