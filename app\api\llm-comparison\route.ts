import { NextRequest, NextResponse } from "next/server";
import { llmTool, Llm<PERSON><PERSON>ider } from "../../../lib/tools/llm-tool";

/**
 * Handle investigative research optimization
 */
async function handleInvestigativeResearchOptimization({
  prompt,
  criteria,
  models
}: {
  prompt: string;
  criteria?: Record<string, string>;
  models: {
    criteriaModel?: string;
    optimizationModel?: string;
    assessmentModel?: string;
    consolidationModel?: string;
  };
}) {
  try {
    const optimizationPrompt = `
You are an expert investigative research consultant tasked with optimizing a research request for professional investigative analysis.

Original Request: "${prompt}"

Optimization Criteria:
${criteria ? Object.entries(criteria).map(([key, value]) => `- ${key}: ${value}`).join('\n') : `
- Investigation Clarity: Clear investigation objectives and scope
- Evidence Specification: Specific types of evidence and sources needed
- Verification Requirements: Multi-source verification methodology
- Fact-checking Standards: Professional fact-checking protocols
- Reporting Standards: Investigative journalism reporting standards
- Ethical Considerations: Ethical guidelines for investigative research
`}

Please optimize this request to:
1. Clearly define the investigation objectives and scope
2. Specify the types of evidence and sources needed
3. Outline multi-source verification requirements

Provide an optimized investigative research request that is comprehensive, 
professional, and actionable for specialized investigative research agents.

Return only the optimized request without additional commentary.
`;

    const optimizationProvider: LlmProvider = models.optimizationModel?.includes("gemini") ? "google" :
                                            models.optimizationModel?.includes("claude") ? "anthropic" : "openai";

    const optimizedPrompt = await llmTool.processContent({
      prompt: optimizationPrompt,
      model: models.optimizationModel || "gpt-4o",
      provider: optimizationProvider
    });

    return NextResponse.json({
      success: true,
      optimizedPrompt: optimizedPrompt.trim(),
      metadata: {
        optimizationType: 'investigative-research',
        models: models,
        timestamp: new Date().toISOString()
      }
    });

  } catch (error: any) {
    console.error("Error in investigative research optimization:", error);
    return NextResponse.json({
      success: false,
      error: error.message || "Failed to optimize for investigative research"
    }, { status: 500 });
  }
}

interface LlmComparisonRequest {
  action?: 'compare' | 'optimize';
  prompt: string;
  context?: string;
  criteriaModel?: string;
  optimizationModel?: string;
  comparisonModels?: {
    model: string;
    provider: LlmProvider;
  }[] | string[];
  assessmentModel?: string;
  consolidate?: boolean;
  consolidationModel?: string;
  optimizationType?: 'standard' | 'investigative-research';
  criteria?: Record<string, string>;
  models?: {
    criteriaModel?: string;
    optimizationModel?: string;
    assessmentModel?: string;
    consolidationModel?: string;
  };
}

/**
 * API route for LLM comparison
 */
export async function POST(request: NextRequest) {
  try {
    const {
      action = 'compare',
      prompt,
      context = "",
      criteriaModel = "gemini-2.5-pro",
      optimizationModel = "gpt-4o",
      comparisonModels,
      assessmentModel = "claude-sonnet-4-0",
      consolidate = false,
      consolidationModel = "o3-mini-2025-01-31",
      optimizationType = 'standard',
      criteria: customCriteria,
      models
    }: LlmComparisonRequest = await request.json();

    // Handle optimization action for investigative research
    if (action === 'optimize' && optimizationType === 'investigative-research') {
      return await handleInvestigativeResearchOptimization({
        prompt,
        criteria: customCriteria,
        models: models || {
          criteriaModel,
          optimizationModel,
          assessmentModel,
          consolidationModel
        }
      });
    }

    // Validate required parameters
    if (!prompt) {
      return NextResponse.json({
        success: false,
        error: "Prompt is required"
      }, { status: 400 });
    }

    if (action === 'compare' && (!comparisonModels || !Array.isArray(comparisonModels) || comparisonModels.length === 0)) {
      return NextResponse.json({
        success: false,
        error: "At least one comparison model is required for comparison action"
      }, { status: 400 });
    }

    // Normalize comparisonModels to handle both string[] and object[] formats
    const normalizedComparisonModels = comparisonModels ? comparisonModels.map(model => {
      if (typeof model === 'string') {
        // Determine provider based on model name
        const provider: LlmProvider = model.includes('gemini') ? 'google' :
                                     model.includes('claude') ? 'anthropic' :
                                     model.includes('gpt') || model.includes('o1') || model.includes('o3') ? 'openai' : 'groq';
        return { model, provider };
      }
      return model;
    }) : [];

    // Step 1: Generate criteria using Gemini
    console.log("Generating criteria...");
    const criteriaPrompt = `
You are an expert at evaluating AI responses. I need you to establish clear criteria for evaluating responses to the following prompt:

"${prompt}"

Please generate 5-7 specific criteria that a high-quality response should meet. Each criterion should be:
1. Specific and measurable
2. Relevant to the prompt's requirements
3. Focused on different aspects (e.g., accuracy, completeness, creativity, etc.)

Format your response as a numbered list with a brief explanation for each criterion.
`;

    const criteriaProvider: LlmProvider = criteriaModel.includes("gemini") ? "google" : "groq";
    const criteria = await llmTool.processContent({
      prompt: criteriaPrompt,
      model: criteriaModel,
      provider: criteriaProvider
    });

    // Step 2: Optimize the prompt using OpenAI 4o
    console.log("Optimizing prompt...");
    const optimizationPrompt = `
You are an expert at crafting clear and effective prompts for AI language models. I need you to optimize the following prompt to ensure it produces the highest quality response:

"${prompt}"

Please analyze this prompt and improve it by:
1. Clarifying any ambiguous instructions
2. Adding specific details about the expected format and content
3. Structuring it to guide the AI jo toward a comprehensive and well-organized response
4. Ensuring it communicates all necessary context and requirements

Return only the optimized prompt without explanations or additional text.
`;

    const optimizedPrompt = await llmTool.processContent({
      prompt: optimizationPrompt,
      model: optimizationModel,
      provider: "openai" as LlmProvider
    });

    // Step 3: Generate responses from each model
    console.log("Generating responses from comparison models...");
    const modelResponses = await Promise.all(
      normalizedComparisonModels.map(async (modelConfig, index) => {
        try {
          const response = await llmTool.processContent({
            prompt: optimizedPrompt,
            context,
            model: modelConfig.model,
            provider: modelConfig.provider
          });

          return {
            model: modelConfig.model,
            provider: modelConfig.provider,
            response,
            error: null
          };
        } catch (error: any) {
          console.error(`Error with model ${modelConfig.model}:`, error);
          return {
            model: modelConfig.model,
            provider: modelConfig.provider,
            response: null,
            error: error.message || `Failed to generate response with ${modelConfig.model}`
          };
        }
      })
    );

    // Step 4: Assess the responses using Claude 4.0 (with fallback to Gemini)
    console.log("Assessing responses...");
    const validResponses = modelResponses.filter(r => r.response !== null);

    if (validResponses.length === 0) {
      return NextResponse.json({
        success: false,
        error: "All models failed to generate responses"
      }, { status: 500 });
    }

    const assessmentPrompt = `
You are an expert at evaluating AI-generated content. I need you to compare and assess responses from different AI models to the following prompt:

ORIGINAL PROMPT:
"${prompt}"

OPTIMIZED PROMPT:
"${optimizedPrompt}"

EVALUATION CRITERIA:
${criteria}

Here are the responses from different models:

${validResponses.map((r, i) => `MODEL ${i+1} (${r.model} via ${r.provider}):
${r.response}

`).join('\n')}

Please provide:
1. A detailed assessment of each response based on the criteria
2. A comparative analysis highlighting the strengths and weaknesses of each
3. A clear determination of which response is best overall with justification
4. Specific pros and cons for each response

Format your assessment with clear headings and organized sections.
`;

    let assessment = "";
    let assessmentModelUsed = assessmentModel;
    let assessmentProviderUsed: LlmProvider = "anthropic";

    try {
      // Try with Claude first
      assessment = await llmTool.processContent({
        prompt: assessmentPrompt,
        model: assessmentModel,
        provider: "anthropic" as LlmProvider
      });
    } catch (error) {
      console.error("Error with Claude assessment, falling back to Gemini:", error);
      // Fallback to Gemini
      try {
        assessmentModelUsed = "gemini-2.5-flash-preview-04-17";
        assessmentProviderUsed = "google";
        assessment = await llmTool.processContent({
          prompt: assessmentPrompt,
          model: assessmentModelUsed,
          provider: assessmentProviderUsed
        });
      } catch (fallbackError) {
        console.error("Error with fallback assessment:", fallbackError);
        throw new Error("Both primary and fallback assessment models failed");
      }
    }

    // Step 5: Optional - Consolidate responses
    let consolidatedResponse = null;
    if (consolidate) {
      console.log("Consolidating responses...");
      const consolidationPrompt = `
You are tasked with creating a consolidated response that combines the best elements from multiple AI-generated responses.

ORIGINAL PROMPT:
"${prompt}"

EVALUATION CRITERIA:
${criteria}

ASSESSMENT OF RESPONSES:
${assessment}

INDIVIDUAL RESPONSES:
${validResponses.map((r, i) => `MODEL ${i+1} (${r.model} via ${r.provider}):
${r.response}

`).join('\n')}

Please create a single consolidated response that:
1. Incorporates the strongest elements from each individual response
2. Addresses all aspects of the original prompt
3. Meets all the evaluation criteria
4. Avoids the weaknesses identified in the individual responses
5. Is coherent, well-structured, and reads as a unified whole

Return only the consolidated response without explanations or additional text.
`;

      consolidatedResponse = await llmTool.processContent({
        prompt: consolidationPrompt,
        model: consolidationModel,
        provider: "openai" as LlmProvider
      });
    }

    // Return the complete results with model information
    return NextResponse.json({
      success: true,
      criteria,
      criteriaModel,
      criteriaProvider: criteriaProvider,
      optimizedPrompt,
      optimizationModel,
      optimizationProvider: "openai",
      modelResponses,
      assessment,
      assessmentModel: assessmentModelUsed,
      assessmentProvider: assessmentProviderUsed,
      consolidatedResponse,
      consolidationModel: consolidate ? consolidationModel : null,
      consolidationProvider: consolidate ? "openai" : null
    });
  } catch (error: any) {
    console.error("Error in LLM comparison API:", error);
    return NextResponse.json({
      success: false,
      error: error.message || "An error occurred during LLM comparison"
    }, { status: 500 });
  }
}