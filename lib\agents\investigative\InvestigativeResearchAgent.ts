/**
 * Investigative Research Agent
 * 
 * A specialized agent that leverages multi-LLM comparison for comprehensive investigative research.
 * Integrates with the PMO ecosystem and provides deep, multi-perspective analysis through
 * specialized journalist sub-agents.
 */

import { LlmProvider, llmTool } from '../../tools/llm-tool';
import { AgentMemoryManager } from '../AgentMemoryManager';
import { AgenticTeamId } from '../pmo/PMOInterfaces';
import { internetSearchTool, SearchResultItem } from '../../tools/internet-search';
import { QueryDocumentsAgent, QueryDocumentsAgentResult } from '../../../components/Agents/QueryDocumentsAgent';
import { QuestionAnswerAgent, QuestionAnswerResult } from '../../../components/Agents/QuestionAnswerAgent';
import { getProviderForModel } from './constants';

// Investigation Types
export enum InvestigationType {
  FINANCIAL = 'financial',
  POLITICAL = 'political',
  TECHNOLOGY = 'technology',
  SOCIAL_AFFAIRS = 'social_affairs',
  CORPORATE = 'corporate',
  ENVIRONMENTAL = 'environmental',
  INVESTIGATIVE = 'investigative',
  FEATURE = 'feature'
}

// Journalist Sub-Agent Personas
export interface JournalistPersona {
  id: string;
  name: string;
  specialty: InvestigationType;
  description: string;
  preferredModel: string;
  preferredProvider: LlmProvider;
  expertise: string[];
  investigationStyle: string;
}

// Model Configuration for Investigation
export interface InvestigationModelConfig {
  model: string;
  provider: LlmProvider;
  persona?: JournalistPersona;
}

// Investigation Request
export interface InvestigationRequest {
  prompt: string;
  context?: string;
  investigationType: InvestigationType;
  selectedJournalists?: string[]; // Journalist persona IDs (optional - can use comparisonModels instead)
  comparisonModels?: Array<{model: string, provider: LlmProvider}> | string[]; // LLM comparison models
  criteriaModel?: string;
  optimizationModel?: string;
  assessmentModel?: string;
  consolidate?: boolean;
  consolidationModel?: string;
  userId: string;
  pmoId?: string;
  // Enhanced research capabilities
  enableInternetSearch?: boolean; // Enable internet search for current information (default: true)
  enableDocumentAnalysis?: boolean; // Enable internal document analysis (default: true)
  documentCategory?: string; // Specific document category to search
}

// Investigation Response from Individual Journalist
export interface JournalistResponse {
  journalistId: string;
  journalistName: string;
  model: string;
  provider: LlmProvider;
  response: string | null;
  error: string | null;
  investigationAngle: string;
  keyFindings: string[];
}

// Complete Investigation Result
export interface InvestigationResult {
  investigationId: string;
  originalPrompt: string;
  optimizedPrompt: string;
  investigationType: InvestigationType;
  criteria: string;
  criteriaModel: string;
  criteriaProvider: LlmProvider;
  optimizationModel: string;
  optimizationProvider: LlmProvider;
  journalistResponses: JournalistResponse[];
  assessment: string;
  assessmentModel: string;
  assessmentProvider: LlmProvider;
  consolidatedReport: string | null;
  consolidationModel: string | null;
  consolidationProvider: LlmProvider | null;
  keyFindings: string[];
  recommendations: string[];
  sources: string[];
  createdAt: Date;
  userId: string;
  pmoId?: string;
}

// Progress Step for Investigation
export type InvestigationProgressStep = 
  | 'idle' 
  | 'criteria' 
  | 'optimization' 
  | 'investigation' 
  | 'assessment' 
  | 'consolidation' 
  | 'complete';

/**
 * Investigative Research Agent Class
 */
export class InvestigativeResearchAgent {
  private memoryManager: AgentMemoryManager;
  private userId: string;
  private journalistPersonas: Map<string, JournalistPersona> = new Map();
  private queryDocumentsAgent: QueryDocumentsAgent;
  private questionAnswerAgent: QuestionAnswerAgent;

  constructor(userId: string) {
    this.userId = userId;
    this.memoryManager = new AgentMemoryManager(userId);
    this.initializeJournalistPersonas();

    // Initialize document analysis agents following StrategicDirectorAgent pattern
    this.queryDocumentsAgent = new QueryDocumentsAgent({
      maxResults: 2,
      defaultTemperature: 0.3,
      defaultMaxTokens: 3000
    });

    this.questionAnswerAgent = new QuestionAnswerAgent();
  }

  /**
   * Initialize predefined journalist personas
   */
  private initializeJournalistPersonas(): void {
    const personas: JournalistPersona[] = [
      {
        id: 'investigative-journalist',
        name: 'Investigative Journalist',
        specialty: InvestigationType.INVESTIGATIVE,
        description: 'Deep-dive investigative reporting with focus on uncovering hidden truths',
        preferredModel: 'claude-sonnet-4-0',
        preferredProvider: 'anthropic',
        expertise: ['investigative techniques', 'source verification', 'document analysis', 'whistleblower protection'],
        investigationStyle: 'Methodical, thorough, skeptical, evidence-based'
      },
      {
        id: 'financial-reporter',
        name: 'Financial Reporter',
        specialty: InvestigationType.FINANCIAL,
        description: 'Specialized in financial markets, corporate finance, and economic analysis',
        preferredModel: 'gpt-4o',
        preferredProvider: 'openai',
        expertise: ['financial analysis', 'market trends', 'corporate governance', 'regulatory compliance'],
        investigationStyle: 'Data-driven, analytical, focused on financial implications'
      },
      {
        id: 'political-correspondent',
        name: 'Political Correspondent',
        specialty: InvestigationType.POLITICAL,
        description: 'Expert in political systems, policy analysis, and government operations',
        preferredModel: 'gemini-2.5-pro',
        preferredProvider: 'google',
        expertise: ['political analysis', 'policy implications', 'government processes', 'electoral systems'],
        investigationStyle: 'Balanced, contextual, focused on policy implications'
      },
      {
        id: 'technology-analyst',
        name: 'Technology Analyst',
        specialty: InvestigationType.TECHNOLOGY,
        description: 'Covers technology trends, cybersecurity, and digital transformation',
        preferredModel: 'gpt-4o',
        preferredProvider: 'openai',
        expertise: ['technology trends', 'cybersecurity', 'digital privacy', 'innovation analysis'],
        investigationStyle: 'Technical, forward-looking, innovation-focused'
      },
      {
        id: 'social-affairs-reporter',
        name: 'Social Affairs Reporter',
        specialty: InvestigationType.SOCIAL_AFFAIRS,
        description: 'Focuses on social issues, community impact, and human interest stories',
        preferredModel: 'claude-sonnet-4-0',
        preferredProvider: 'anthropic',
        expertise: ['social issues', 'community impact', 'human rights', 'demographic analysis'],
        investigationStyle: 'Empathetic, community-focused, human-centered'
      },
      {
        id: 'feature-reporter',
        name: 'Feature Reporter',
        specialty: InvestigationType.FEATURE,
        description: 'Long-form storytelling with narrative depth and comprehensive context',
        preferredModel: 'claude-sonnet-4-0',
        preferredProvider: 'anthropic',
        expertise: ['narrative storytelling', 'long-form writing', 'cultural analysis', 'historical context'],
        investigationStyle: 'Narrative-driven, contextual, comprehensive'
      }
    ];

    personas.forEach(persona => {
      this.journalistPersonas.set(persona.id, persona);
    });
  }

  /**
   * Get available journalist personas
   */
  getJournalistPersonas(): JournalistPersona[] {
    return Array.from(this.journalistPersonas.values());
  }

  /**
   * Get journalist personas by investigation type
   */
  getJournalistsByType(investigationType: InvestigationType): JournalistPersona[] {
    return this.getJournalistPersonas().filter(
      persona => persona.specialty === investigationType || persona.specialty === InvestigationType.INVESTIGATIVE
    );
  }

  /**
   * Get journalist persona by ID
   */
  getJournalistPersona(journalistId: string): JournalistPersona | undefined {
    return this.journalistPersonas.get(journalistId);
  }

  /**
   * Create investigation configurations from LLM comparison models
   */
  private createLLMComparisonConfigs(
    comparisonModels: Array<{model: string, provider: LlmProvider}> | string[],
    investigationType: InvestigationType
  ): Array<{
    journalistName: string;
    journalistId: string;
    model: string;
    provider: LlmProvider;
    investigationAngle: string;
    expertise: string[];
    investigationStyle: string;
  }> {
    // Normalize comparison models to handle both string[] and object[] formats
    const normalizedModels = comparisonModels.map((model, index) => {
      if (typeof model === 'string') {
        // Determine provider based on model name
        const provider: LlmProvider = model.includes('gemini') ? 'google' :
                                     model.includes('claude') ? 'anthropic' :
                                     model.includes('gpt') || model.includes('o1') || model.includes('o3') ? 'openai' : 'groq';
        return { model, provider };
      }
      return model;
    });

    // Create investigation configurations for each model
    return normalizedModels.map((modelConfig, index) => {
      const investigationAngles = [
        'analytical and data-driven perspective',
        'comprehensive and methodical perspective',
        'critical and investigative perspective'
      ];

      const investigationStyles = [
        'Analytical, evidence-based, systematic',
        'Comprehensive, thorough, methodical',
        'Critical, investigative, skeptical'
      ];

      const expertiseByType = {
        [InvestigationType.FINANCIAL]: ['financial analysis', 'market trends', 'economic indicators', 'regulatory compliance'],
        [InvestigationType.POLITICAL]: ['political analysis', 'policy implications', 'government processes', 'electoral systems'],
        [InvestigationType.TECHNOLOGY]: ['technology trends', 'cybersecurity', 'digital innovation', 'tech policy'],
        [InvestigationType.SOCIAL_AFFAIRS]: ['social issues', 'community impact', 'demographic analysis', 'human rights'],
        [InvestigationType.CORPORATE]: ['corporate governance', 'business strategy', 'organizational analysis', 'compliance'],
        [InvestigationType.ENVIRONMENTAL]: ['environmental policy', 'sustainability', 'climate analysis', 'regulatory impact'],
        [InvestigationType.INVESTIGATIVE]: ['investigative techniques', 'source verification', 'document analysis', 'fact-checking'],
        [InvestigationType.FEATURE]: ['narrative analysis', 'contextual research', 'comprehensive reporting', 'storytelling']
      };

      return {
        journalistName: `${modelConfig.model} Investigative Analyst`,
        journalistId: `llm-comparison-${index + 1}`,
        model: modelConfig.model,
        provider: modelConfig.provider,
        investigationAngle: investigationAngles[index % investigationAngles.length],
        expertise: expertiseByType[investigationType] || expertiseByType[InvestigationType.INVESTIGATIVE],
        investigationStyle: investigationStyles[index % investigationStyles.length]
      };
    });
  }

  /**
   * Perform internet search for investigation research
   * @param query - Search query
   * @param numResults - Number of results to return (default: 5)
   * @returns Search results
   */
  async performInternetSearch(query: string, numResults: number = 5): Promise<{
    success: boolean;
    results: SearchResultItem[];
    formattedResults: string;
    error?: string;
  }> {
    try {
      console.log(`InvestigativeResearchAgent: Performing internet search for "${query}"`);

      // Add rate limiting delay to prevent "Too Many Requests" errors
      await this.rateLimitDelay();

      const searchResult = await internetSearchTool.search(query, { numResults });

      return {
        success: searchResult.success,
        results: searchResult.results,
        formattedResults: searchResult.formattedResults,
        error: searchResult.metadata.error
      };
    } catch (error) {
      console.error('InvestigativeResearchAgent: Internet search failed:', error);

      // Check if it's a rate limit error and provide helpful message
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      if (errorMessage.includes('Too Many Requests')) {
        console.log('InvestigativeResearchAgent: Rate limit hit, investigation will continue without internet search for this journalist');
        return {
          success: false,
          results: [],
          formattedResults: '',
          error: 'Rate limit exceeded - continuing investigation without internet search'
        };
      }

      return {
        success: false,
        results: [],
        formattedResults: '',
        error: errorMessage
      };
    }
  }

  /**
   * Rate limiting delay to prevent API overload
   */
  private async rateLimitDelay(): Promise<void> {
    // Add a 2-second delay between internet searches to respect API rate limits
    await new Promise(resolve => setTimeout(resolve, 2000));
  }

  /**
   * Create a concise search query from the optimized prompt
   * @param optimizedPrompt - The full optimized prompt
   * @param specialty - The journalist specialty
   * @returns A concise search query suitable for API limits
   */
  private createSearchQuery(optimizedPrompt: string, specialty: string): string {
    // Extract key terms from the prompt (limit to 100 characters for API compatibility)
    let searchQuery = optimizedPrompt;

    // Remove common investigation prefixes that don't help with search
    searchQuery = searchQuery.replace(/^(OPTIMIZED RESEARCH QUESTION:|Investigate and analyze|Research|Analyze)/i, '');

    // Remove placeholder text in brackets
    searchQuery = searchQuery.replace(/\[.*?\]/g, '');

    // Clean up extra whitespace
    searchQuery = searchQuery.trim().replace(/\s+/g, ' ');

    // Truncate to reasonable length for search APIs (most support up to 200 chars)
    if (searchQuery.length > 150) {
      // Find the last complete word within 150 characters
      const truncated = searchQuery.substring(0, 150);
      const lastSpace = truncated.lastIndexOf(' ');
      searchQuery = lastSpace > 100 ? truncated.substring(0, lastSpace) : truncated;
    }

    // Add specialty context if there's room
    if (searchQuery.length < 120) {
      searchQuery += ` ${specialty}`;
    }

    console.log(`Created search query (${searchQuery.length} chars): "${searchQuery}"`);
    return searchQuery;
  }

  /**
   * Query internal documents for investigation context
   * @param query - Document query
   * @param category - Optional document category
   * @param useInternetSearch - Whether to use internet search as fallback
   * @returns Document query results
   */
  async queryDocuments(
    query: string,
    category?: string,
    useInternetSearch: boolean = true
  ): Promise<QueryDocumentsAgentResult> {
    try {
      console.log(`InvestigativeResearchAgent: Querying documents for "${query}"`);

      const result = await this.queryDocumentsAgent.process({
        query,
        userId: this.userId,
        category,
        useInternetSearch,
        model: 'claude-sonnet-4-0'
      });

      return result;
    } catch (error) {
      console.error('InvestigativeResearchAgent: Document query failed:', error);
      return {
        success: false,
        content: '',
        error: error instanceof Error ? error.message : 'Unknown error occurred during document query',
        followUpQuestions: [],
        relatedCategories: [],
        matchingDocuments: [],
        sources: [],
        metadata: { internetSearchUsed: false, functionCallingUsed: false }
      };
    }
  }

  /**
   * Use QuestionAnswerAgent to generate and answer specific questions about the investigation
   * @param userRequest - The investigation request
   * @param context - Additional context from research
   * @param category - Document category for focused search
   * @returns Question-answer analysis results
   */
  async analyzeWithQuestions(
    userRequest: string,
    context?: string,
    category?: string
  ): Promise<QuestionAnswerResult> {
    try {
      console.log(`InvestigativeResearchAgent: Analyzing with QuestionAnswerAgent for "${userRequest}"`);

      const result = await this.questionAnswerAgent.process({
        userRequest,
        context,
        userId: this.userId,
        category,
        maxQuestions: 3,
        enabledTools: {
          internetSearch: true,
          calculator: false,
          calendar: false
        }
      });

      return result;
    } catch (error) {
      console.error('InvestigativeResearchAgent: QuestionAnswerAgent analysis failed:', error);
      return {
        success: false,
        questions: [],
        error: error instanceof Error ? error.message : 'Unknown error occurred during question analysis'
      };
    }
  }

  /**
   * Conduct comprehensive investigation using multi-LLM comparison
   */
  async conductInvestigation(request: InvestigationRequest): Promise<InvestigationResult> {
    // Use PMO ID as investigation ID if available, otherwise generate one
    const investigationId = request.pmoId || `inv_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    console.log(`Starting investigation ${investigationId} for user ${request.userId}`);

    try {
      // Step 1: Generate investigation criteria
      const criteria = await this.generateInvestigationCriteria(request);

      // Step 2: Optimize the investigation prompt
      const optimizedPrompt = await this.optimizeInvestigationPrompt(request);

      // Step 3: Conduct investigation with selected journalists
      const journalistResponses = await this.conductJournalistInvestigations(request, optimizedPrompt);

      // Step 4: Assess and compare journalist findings
      const assessment = await this.assessJournalistFindings(request, criteria, journalistResponses);

      // Step 5: Optional consolidation
      let consolidatedReport = null;
      if (request.consolidate) {
        consolidatedReport = await this.consolidateFindings(request, criteria, assessment, journalistResponses);
      }

      // Extract key findings and recommendations
      const keyFindings = this.extractKeyFindings(journalistResponses);
      const recommendations = this.extractRecommendations(assessment, consolidatedReport);
      const sources = this.extractSources(journalistResponses);

      const result: InvestigationResult = {
        investigationId,
        originalPrompt: request.prompt,
        optimizedPrompt,
        investigationType: request.investigationType,
        criteria,
        criteriaModel: request.criteriaModel || 'gemini-2.5-pro',
        criteriaProvider: getProviderForModel(request.criteriaModel || 'gemini-2.5-pro') as LlmProvider,
        optimizationModel: request.optimizationModel || 'gpt-4o',
        optimizationProvider: getProviderForModel(request.optimizationModel || 'gpt-4o') as LlmProvider,
        journalistResponses,
        assessment,
        assessmentModel: request.assessmentModel || 'claude-sonnet-4-0',
        assessmentProvider: getProviderForModel(request.assessmentModel || 'claude-sonnet-4-0') as LlmProvider,
        consolidatedReport,
        consolidationModel: request.consolidate ? (request.consolidationModel || 'gpt-4o') : null,
        consolidationProvider: request.consolidate ? getProviderForModel(request.consolidationModel || 'gpt-4o') as LlmProvider : null,
        keyFindings,
        recommendations,
        sources,
        createdAt: new Date(),
        userId: request.userId,
        pmoId: request.pmoId
      };

      // Store investigation result
      await this.storeInvestigationResult(result);

      return result;

    } catch (error) {
      console.error(`Investigation ${investigationId} failed:`, error);
      throw new Error(`Investigation failed: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Generate investigation criteria based on the investigation type and prompt
   */
  private async generateInvestigationCriteria(request: InvestigationRequest): Promise<string> {
    const criteriaPrompt = `
You are an expert investigative journalism editor. Generate comprehensive evaluation criteria for assessing investigative research on the following topic:

INVESTIGATION TYPE: ${request.investigationType}
RESEARCH QUESTION: "${request.prompt}"
${request.context ? `ADDITIONAL CONTEXT: ${request.context}` : ''}

Please generate 7-10 specific criteria that high-quality investigative research should meet for this type of investigation. Each criterion should be:
1. Specific and measurable for investigative journalism standards
2. Relevant to ${request.investigationType} investigations
3. Focused on different aspects (accuracy, depth, sources, impact, ethics, etc.)
4. Appropriate for evaluating multiple journalist perspectives

Consider these investigative journalism standards:
- Source verification and credibility
- Factual accuracy and evidence quality
- Depth of investigation and analysis
- Ethical considerations and bias awareness
- Impact and public interest relevance
- Clarity and accessibility of reporting
- Use of investigative techniques and methodology

Format your response as a numbered list with brief explanations for each criterion.
`;

    const criteriaModel = request.criteriaModel || 'gemini-2.5-pro';
    const criteriaProvider: LlmProvider = getProviderForModel(criteriaModel) as LlmProvider;

    return await llmTool.processContent({
      prompt: criteriaPrompt,
      model: criteriaModel,
      provider: criteriaProvider
    });
  }

  /**
   * Optimize the investigation prompt for better journalist responses
   */
  private async optimizeInvestigationPrompt(request: InvestigationRequest): Promise<string> {
    const optimizationPrompt = `
You are an expert investigative journalism editor. Optimize the following research question to ensure it produces the highest quality investigative responses from specialized journalists:

ORIGINAL RESEARCH QUESTION: "${request.prompt}"
INVESTIGATION TYPE: ${request.investigationType}
${request.context ? `CONTEXT: ${request.context}` : ''}

Please analyze and improve this research question by:
1. Clarifying any ambiguous aspects that could lead to unfocused investigation
2. Adding specific investigative angles and approaches to explore
3. Structuring it to guide journalists toward comprehensive analysis
4. Ensuring it encourages evidence-based, source-verified reporting
5. Making it clear what type of investigation depth and scope is expected
6. Including relevant investigative techniques that should be employed

Return only the optimized research question without explanations or additional text. The optimized question should be clear, specific, and actionable for investigative journalists.
`;

    const optimizationModel = request.optimizationModel || 'gpt-4o';
    const optimizationProvider: LlmProvider = getProviderForModel(optimizationModel) as LlmProvider;

    return await llmTool.processContent({
      prompt: optimizationPrompt,
      model: optimizationModel,
      provider: optimizationProvider
    });
  }

  /**
   * Conduct investigations with selected journalist personas or comparison models
   */
  private async conductJournalistInvestigations(
    request: InvestigationRequest,
    optimizedPrompt: string
  ): Promise<JournalistResponse[]> {
    let investigationConfigs: Array<{
      journalistName: string;
      journalistId: string;
      model: string;
      provider: LlmProvider;
      investigationAngle: string;
      expertise: string[];
      investigationStyle: string;
    }> = [];

    // Determine investigation approach: journalist personas vs comparison models
    if (request.comparisonModels && request.comparisonModels.length > 0) {
      // Use LLM comparison methodology with investigative prompting
      console.log('Using LLM comparison methodology for investigation');
      investigationConfigs = this.createLLMComparisonConfigs(request.comparisonModels, request.investigationType);
    } else if (request.selectedJournalists && request.selectedJournalists.length > 0) {
      // Use traditional journalist persona approach
      console.log('Using journalist persona methodology for investigation');
      const selectedJournalists = request.selectedJournalists.map(id => this.getJournalistPersona(id)).filter(Boolean) as JournalistPersona[];
      investigationConfigs = selectedJournalists.map(journalist => ({
        journalistName: journalist.name,
        journalistId: journalist.id,
        model: journalist.preferredModel,
        provider: journalist.preferredProvider,
        investigationAngle: `${journalist.specialty} perspective with ${journalist.investigationStyle} approach`,
        expertise: journalist.expertise,
        investigationStyle: journalist.investigationStyle
      }));
    } else {
      // Fallback: use default journalists for investigation type
      console.log('No specific selection provided, using default journalists for investigation type');
      const defaultJournalists = this.getJournalistsByType(request.investigationType).slice(0, 3);
      investigationConfigs = defaultJournalists.map(journalist => ({
        journalistName: journalist.name,
        journalistId: journalist.id,
        model: journalist.preferredModel,
        provider: journalist.preferredProvider,
        investigationAngle: `${journalist.specialty} perspective with ${journalist.investigationStyle} approach`,
        expertise: journalist.expertise,
        investigationStyle: journalist.investigationStyle
      }));
    }

    if (investigationConfigs.length === 0) {
      throw new Error('No valid investigation configurations available');
    }

    console.log(`Conducting investigation with ${investigationConfigs.length} configurations`);

    // Generate responses from each investigation configuration sequentially to avoid rate limits
    const journalistResponses: JournalistResponse[] = [];

    for (const config of investigationConfigs) {
      try {
        // Step 1: Gather research context based on enabled options
        console.log(`${config.journalistName}: Gathering research context for investigation`);

        let researchContext = '';

        // Perform internet search if enabled (default: true)
        if (request.enableInternetSearch !== false) {
          // Create a concise search query to avoid API limits
          const searchQuery = this.createSearchQuery(optimizedPrompt, request.investigationType);
          const internetSearchResults = await this.performInternetSearch(
            searchQuery,
            5
          );

          if (internetSearchResults.success && internetSearchResults.results.length > 0) {
            researchContext += `\n**CURRENT INTERNET RESEARCH:**\n${internetSearchResults.formattedResults}\n`;
          }
        }

        // Query internal documents if enabled (default: true)
        if (request.enableDocumentAnalysis !== false) {
          const documentResults = await this.queryDocuments(
            `${optimizedPrompt} ${request.investigationType}`,
            request.documentCategory, // Use specific category if provided
            false // Don't use internet search as fallback since we handle it separately
          );

          if (documentResults.success && documentResults.content) {
            researchContext += `\n**INTERNAL DOCUMENT ANALYSIS:**\n${documentResults.content}\n`;
          }
        }

        // Step 2: Create enhanced investigative prompt with research context
        const journalistPrompt = `
You are ${config.journalistName}, an expert investigative analyst.

EXPERTISE: ${config.expertise.join(', ')}
INVESTIGATION STYLE: ${config.investigationStyle}
INVESTIGATION ANGLE: ${config.investigationAngle}

RESEARCH QUESTION: "${optimizedPrompt}"
${request.context ? `ADDITIONAL CONTEXT: ${request.context}` : ''}

RESEARCH CONTEXT GATHERED:${researchContext}

INVESTIGATION INSTRUCTIONS:
As ${config.journalistName} with expertise in ${config.expertise.join(', ')}, you have been provided with current internet research and internal document analysis above. Based on this research context, conduct a comprehensive investigative analysis of the topic using your ${config.investigationAngle}.

**IMPORTANT**: Your investigation should demonstrate that you have thoroughly searched the internet and gathered current information. Reference specific sources, dates, and findings from the research context provided above.

Your investigative report must include:

1. **Investigation Approach**: Explain your specific investigative methodology and angle (${config.investigationAngle}), including how you utilized the internet research and document analysis
2. **Current Information Analysis**: Analyze the most recent and relevant information found through internet searches, citing specific sources and dates
3. **Key Findings**: Present your main discoveries and insights based on the comprehensive research context
4. **Evidence Analysis**: Critically analyze available evidence from both internet sources and internal documents, noting credibility and relevance
5. **Source Assessment**: Discuss the credibility, bias, and relevance of all sources found in your research
6. **Cross-Reference Verification**: Compare findings across multiple sources to verify accuracy and identify discrepancies
7. **Implications**: Explore the broader implications of your findings for stakeholders and the public interest
8. **Recommendations**: Provide specific, actionable recommendations based on your investigation
9. **Conclusion**: Provide a comprehensive conclusion that synthesizes all findings and demonstrates thorough investigative work

**RESEARCH STANDARDS**:
- Reference specific sources from the research context when making claims
- Include dates and publication information where available
- Maintain your professional investigative perspective throughout (${config.investigationStyle})
- Be thorough, evidence-based, and maintain journalistic integrity
- Demonstrate that you have conducted comprehensive internet research by citing multiple current sources
- Explain how you gathered and verified information through multiple channels

Your response should be a complete investigative report that clearly shows you have searched the internet, gathered current information, and conducted thorough analysis.
`;

        const response = await llmTool.processContent({
          prompt: journalistPrompt,
          context: request.context,
          model: config.model,
          provider: config.provider
        });

        // Extract key findings from the response
        const keyFindings = this.extractKeyFindingsFromResponse(response);

        const journalistResponse: JournalistResponse = {
          journalistId: config.journalistId,
          journalistName: config.journalistName,
          model: config.model,
          provider: config.provider,
          response,
          error: null,
          investigationAngle: config.investigationAngle,
          keyFindings
        };

        journalistResponses.push(journalistResponse);
        console.log(`${config.journalistName}: Investigation completed successfully`);

      } catch (error: any) {
        console.error(`Error with investigative analyst ${config.journalistName}:`, error);

        const errorResponse: JournalistResponse = {
          journalistId: config.journalistId,
          journalistName: config.journalistName,
          model: config.model,
          provider: config.provider,
          response: null,
          error: error.message || `Failed to generate investigation from ${config.journalistName}`,
          investigationAngle: config.investigationAngle,
          keyFindings: []
        };

        journalistResponses.push(errorResponse);
      }
    }

    return journalistResponses;
  }

  /**
   * Assess and compare journalist findings
   */
  private async assessJournalistFindings(
    request: InvestigationRequest,
    criteria: string,
    journalistResponses: JournalistResponse[]
  ): Promise<string> {
    const validResponses = journalistResponses.filter(r => r.response !== null);

    if (validResponses.length === 0) {
      throw new Error('All journalist investigations failed');
    }

    const assessmentPrompt = `
You are a senior investigative journalism editor with expertise in evaluating multi-perspective investigative research. Assess and compare the following investigative findings:

ORIGINAL RESEARCH QUESTION: "${request.prompt}"
INVESTIGATION TYPE: ${request.investigationType}

EVALUATION CRITERIA:
${criteria}

JOURNALIST INVESTIGATIONS:

${validResponses.map((r, i) => `
JOURNALIST ${i + 1}: ${r.journalistName} (${r.investigationAngle})
MODEL: ${r.model} via ${r.provider}

INVESTIGATION FINDINGS:
${r.response}

KEY FINDINGS IDENTIFIED:
${r.keyFindings.map(finding => `• ${finding}`).join('\n')}

---
`).join('\n')}

Please provide a comprehensive editorial assessment that includes:

1. **Individual Assessment**: Evaluate each journalist's investigation based on the criteria
2. **Comparative Analysis**: Compare the different perspectives and approaches
3. **Strengths and Weaknesses**: Identify what each investigation does well and areas for improvement
4. **Consistency Check**: Note areas of agreement and disagreement between investigations
5. **Evidence Quality**: Assess the quality and credibility of evidence presented
6. **Investigation Completeness**: Determine which investigations are most thorough
7. **Editorial Recommendation**: Recommend which investigation provides the best overall analysis
8. **Synthesis Opportunities**: Identify how the investigations could be combined for stronger reporting

Format your assessment with clear headings and provide specific examples from each investigation.
`;

    const assessmentModel = request.assessmentModel || 'claude-sonnet-4-0';
    let assessmentProvider: LlmProvider = getProviderForModel(assessmentModel) as LlmProvider;

    try {
      return await llmTool.processContent({
        prompt: assessmentPrompt,
        model: assessmentModel,
        provider: assessmentProvider
      });
    } catch (error) {
      console.error('Error with primary assessment model, falling back to Gemini:', error);
      // Fallback to Gemini
      try {
        return await llmTool.processContent({
          prompt: assessmentPrompt,
          model: 'gemini-2.5-pro',
          provider: 'google'
        });
      } catch (fallbackError) {
        console.error('Error with fallback assessment:', fallbackError);
        throw new Error('Both primary and fallback assessment models failed');
      }
    }
  }

  /**
   * Consolidate findings from multiple journalists
   */
  private async consolidateFindings(
    request: InvestigationRequest,
    criteria: string,
    assessment: string,
    journalistResponses: JournalistResponse[]
  ): Promise<string> {
    const validResponses = journalistResponses.filter(r => r.response !== null);

    const consolidationPrompt = `
You are a senior investigative editor tasked with creating a comprehensive consolidated investigation report that combines the best elements from multiple journalist investigations.

ORIGINAL RESEARCH QUESTION: "${request.prompt}"
INVESTIGATION TYPE: ${request.investigationType}

EVALUATION CRITERIA:
${criteria}

EDITORIAL ASSESSMENT:
${assessment}

INDIVIDUAL INVESTIGATIONS:
${validResponses.map((r, i) => `
JOURNALIST ${i + 1}: ${r.journalistName}
INVESTIGATION ANGLE: ${r.investigationAngle}

FINDINGS:
${r.response}

KEY FINDINGS:
${r.keyFindings.map(finding => `• ${finding}`).join('\n')}

---
`).join('\n')}

Create a single, comprehensive investigative report that:

1. **Incorporates the strongest evidence** from each investigation
2. **Addresses all aspects** of the original research question
3. **Meets all evaluation criteria** established for this investigation
4. **Resolves contradictions** between different journalist findings
5. **Provides a coherent narrative** that reads as unified investigative reporting
6. **Maintains journalistic integrity** and ethical standards
7. **Includes proper attribution** to different investigative approaches where relevant

The consolidated report should be structured as a professional investigative article with:
- Executive Summary
- Key Findings
- Detailed Investigation
- Evidence Analysis
- Implications and Impact
- Recommendations for Action
- Areas for Further Investigation

Return only the consolidated investigative report without meta-commentary.
`;

    const consolidationModel = request.consolidationModel || 'gpt-4o';
    const consolidationProvider: LlmProvider = getProviderForModel(consolidationModel) as LlmProvider;

    return await llmTool.processContent({
      prompt: consolidationPrompt,
      model: consolidationModel,
      provider: consolidationProvider
    });
  }

  /**
   * Extract key findings from journalist responses
   */
  private extractKeyFindings(journalistResponses: JournalistResponse[]): string[] {
    const allFindings: string[] = [];

    journalistResponses.forEach(response => {
      if (response.keyFindings && response.keyFindings.length > 0) {
        allFindings.push(...response.keyFindings);
      }
    });

    // Remove duplicates and return unique findings
    return [...new Set(allFindings)];
  }

  /**
   * Extract key findings from a single response text
   */
  private extractKeyFindingsFromResponse(response: string): string[] {
    const findings: string[] = [];

    // Look for key findings sections
    const keyFindingsMatch = response.match(/(?:key findings?|main findings?|discoveries?)[:\s]*\n((?:[-•*]\s*.+\n?)+)/gi);

    if (keyFindingsMatch) {
      keyFindingsMatch.forEach(match => {
        const items = match.split('\n').filter(line => line.trim().match(/^[-•*]\s+/));
        items.forEach(item => {
          const cleaned = item.replace(/^[-•*]\s+/, '').trim();
          if (cleaned) findings.push(cleaned);
        });
      });
    }

    // If no structured findings found, extract first few sentences as key points
    if (findings.length === 0) {
      const sentences = response.split(/[.!?]+/).slice(0, 3);
      sentences.forEach(sentence => {
        const cleaned = sentence.trim();
        if (cleaned.length > 20) findings.push(cleaned);
      });
    }

    return findings.slice(0, 5); // Limit to top 5 findings
  }

  /**
   * Extract recommendations from assessment and consolidated report
   */
  private extractRecommendations(assessment: string, consolidatedReport: string | null): string[] {
    const recommendations: string[] = [];
    const sources = [assessment, consolidatedReport].filter(Boolean) as string[];

    sources.forEach(source => {
      const recommendationsMatch = source.match(/(?:recommendations?|next steps?|actions?)[:\s]*\n((?:[-•*]\s*.+\n?)+)/gi);

      if (recommendationsMatch) {
        recommendationsMatch.forEach(match => {
          const items = match.split('\n').filter(line => line.trim().match(/^[-•*]\s+/));
          items.forEach(item => {
            const cleaned = item.replace(/^[-•*]\s+/, '').trim();
            if (cleaned) recommendations.push(cleaned);
          });
        });
      }
    });

    return [...new Set(recommendations)].slice(0, 10); // Remove duplicates, limit to 10
  }

  /**
   * Extract sources mentioned in journalist responses
   */
  private extractSources(journalistResponses: JournalistResponse[]): string[] {
    const sources: string[] = [];

    journalistResponses.forEach(response => {
      if (response.response) {
        // Look for source mentions
        const sourceMatches = response.response.match(/(?:sources?|according to|reports from|data from)[:\s]+([^.!?]+)/gi);

        if (sourceMatches) {
          sourceMatches.forEach(match => {
            const cleaned = match.replace(/(?:sources?|according to|reports from|data from)[:\s]+/gi, '').trim();
            if (cleaned && cleaned.length < 100) sources.push(cleaned);
          });
        }
      }
    });

    return [...new Set(sources)].slice(0, 15); // Remove duplicates, limit to 15
  }

  /**
   * Store investigation result in memory/database
   */
  private async storeInvestigationResult(result: InvestigationResult): Promise<void> {
    try {
      // Store in agent memory for future reference using the correct AgentMemory format
      const agentMemory = {
        Agent_Response: {
          [`investigation_${result.investigationId}`]: {
            investigationId: result.investigationId,
            investigationType: result.investigationType,
            originalPrompt: result.originalPrompt,
            keyFindings: result.keyFindings,
            recommendations: result.recommendations,
            createdAt: result.createdAt.toISOString(),
            pmoId: result.pmoId,
            journalistCount: result.journalistResponses.length,
            hasConsolidation: !!result.consolidatedReport
          }
        }
      };

      await this.memoryManager.saveMemory('investigative-research-agent', agentMemory);

      console.log(`Investigation result ${result.investigationId} stored successfully`);
    } catch (error) {
      console.error('Failed to store investigation result:', error);
      // Don't throw error as this is not critical for the investigation process
    }
  }

  /**
   * Get investigation history for a user
   */
  async getInvestigationHistory(userId: string, limit: number = 10): Promise<any[]> {
    try {
      // Load agent memory and extract investigation results
      const memory = await this.memoryManager.loadMemory('investigative-research-agent');
      const investigations = [];

      if (memory.Agent_Response) {
        for (const [key, value] of Object.entries(memory.Agent_Response)) {
          if (key.startsWith('investigation_') && typeof value === 'object' && value !== null) {
            investigations.push(value);
          }
        }
      }

      // Sort by creation date (newest first) and limit results
      investigations.sort((a: any, b: any) => {
        const dateA = new Date(a.createdAt || 0);
        const dateB = new Date(b.createdAt || 0);
        return dateB.getTime() - dateA.getTime();
      });

      return investigations.slice(0, limit);
    } catch (error) {
      console.error('Failed to retrieve investigation history:', error);
      return [];
    }
  }
}
