/**
 * Usage Example: Enhanced Investigative Research Agent
 * 
 * This file demonstrates how to use the enhanced Investigative Research Agent
 * with integrated internet search and document analysis capabilities.
 */

import { InvestigativeResearchAgent, InvestigationType } from './InvestigativeResearchAgent';
import { InvestigativeResearchAgentManager } from './InvestigativeResearchAgentManager';

/**
 * Example 1: Using standalone internet search
 */
export async function exampleInternetSearch() {
  const agent = new InvestigativeResearchAgent('user-123');
  
  // Perform internet search for current information
  const searchResults = await agent.performInternetSearch(
    'artificial intelligence regulation 2024', 
    5
  );
  
  if (searchResults.success) {
    console.log('Found', searchResults.results.length, 'relevant articles');
    console.log('Formatted results:', searchResults.formattedResults);
  } else {
    console.log('Search failed:', searchResults.error);
  }
  
  return searchResults;
}

/**
 * Example 2: Using document analysis for internal knowledge
 */
export async function exampleDocumentAnalysis() {
  const agent = new InvestigativeResearchAgent('user-123');
  
  // Query internal documents for strategic insights
  const docResults = await agent.queryDocuments(
    'AI governance and regulatory frameworks',
    'strategic-analysis', // Focus on strategic analysis documents
    false // Don't use internet search as fallback
  );
  
  if (docResults.success) {
    console.log('Document analysis completed');
    console.log('Content length:', docResults.content?.length);
    console.log('Sources found:', docResults.sources?.length);
    console.log('Follow-up questions:', docResults.followUpQuestions);
  } else {
    console.log('Document analysis failed:', docResults.error);
  }
  
  return docResults;
}

/**
 * Example 3: Using question-answer analysis for deeper insights
 */
export async function exampleQuestionAnswerAnalysis() {
  const agent = new InvestigativeResearchAgent('user-123');
  
  // Generate and answer specific questions about the topic
  const qaResults = await agent.analyzeWithQuestions(
    'What are the implications of new AI regulations for tech companies?',
    'Focus on compliance costs, innovation impact, and competitive advantages',
    'strategic-analysis'
  );
  
  if (qaResults.success) {
    console.log('Generated', qaResults.questions?.length, 'questions');
    qaResults.questions?.forEach((qa, index) => {
      console.log(`Q${index + 1}: ${qa.question}`);
      console.log(`A${index + 1}: ${qa.answer.substring(0, 100)}...`);
    });
  } else {
    console.log('Question-answer analysis failed:', qaResults.error);
  }
  
  return qaResults;
}

/**
 * Example 4: Full investigation with enhanced capabilities
 */
export async function exampleFullInvestigation() {
  const manager = new InvestigativeResearchAgentManager({
    userId: 'user-123',
    defaultLlmProvider: 'openai',
    defaultLlmModel: 'gpt-4o'
  });
  
  // Create investigation request with enhanced capabilities
  const investigationRequest = {
    pmoId: 'pmo-ai-regulation-001',
    title: 'AI Regulation Impact Analysis',
    description: 'Comprehensive analysis of new AI regulations and their impact on the technology sector',
    investigationType: InvestigationType.TECHNOLOGY,
    selectedJournalistIds: ['technology-journalist', 'investigative-journalist'],
    userId: 'user-123',
    priority: 'High' as const,
    
    // Enhanced research capabilities
    enableInternetSearch: true,        // Get current regulatory updates
    enableDocumentAnalysis: true,      // Leverage internal strategic analysis
    documentCategory: 'strategic-analysis',
    
    // Investigation configuration
    consolidate: true,                 // Consolidate findings from multiple journalists
    criteriaModel: 'gemini-2.5-pro',
    optimizationModel: 'gpt-4o',
    assessmentModel: 'claude-sonnet-4-0',
    consolidationModel: 'o3-2025-04-16'
  };
  
  // Set up progress tracking
  const onProgress = (progress: any) => {
    console.log(`Progress: ${progress.percentage}% - ${progress.currentActivity}`);
  };
  
  try {
    console.log('Starting comprehensive AI regulation investigation...');
    
    const result = await manager.conductPMOInvestigation(
      investigationRequest,
      onProgress
    );
    
    console.log('Investigation completed successfully!');
    console.log('Investigation ID:', result.investigationId);
    console.log('Journalists involved:', result.journalistResponses.length);
    console.log('Key findings:', result.keyFindings?.length);
    console.log('Recommendations:', result.recommendations?.length);
    console.log('Has consolidated report:', !!result.consolidatedReport);
    
    return result;
    
  } catch (error) {
    console.error('Investigation failed:', error);
    throw error;
  }
}

/**
 * Example 5: Combining multiple research approaches
 */
export async function exampleCombinedResearch() {
  const agent = new InvestigativeResearchAgent('user-123');
  
  console.log('=== Combined Research Approach ===');
  
  // Step 1: Start with internet search for current context
  console.log('Step 1: Gathering current information...');
  const internetResults = await agent.performInternetSearch(
    'blockchain technology enterprise adoption 2024',
    3
  );
  
  // Step 2: Query internal documents for strategic context
  console.log('Step 2: Analyzing internal strategic documents...');
  const docResults = await agent.queryDocuments(
    'blockchain enterprise implementation strategies',
    'strategic-analysis',
    false
  );
  
  // Step 3: Generate specific questions for deeper analysis
  console.log('Step 3: Generating analytical questions...');
  const qaResults = await agent.analyzeWithQuestions(
    'How should enterprises approach blockchain adoption given current market conditions?',
    `Internet Research: ${internetResults.formattedResults}\n\nInternal Analysis: ${docResults.content}`,
    'strategic-analysis'
  );
  
  // Combine all insights
  const combinedInsights = {
    currentMarketTrends: internetResults.success ? internetResults.results : [],
    internalStrategicContext: docResults.success ? docResults.content : '',
    analyticalQuestions: qaResults.success ? qaResults.questions : [],
    researchSources: [
      ...(internetResults.success ? internetResults.results.map(r => r.link) : []),
      ...(docResults.sources || [])
    ]
  };
  
  console.log('Combined research completed:');
  console.log('- Market trends found:', combinedInsights.currentMarketTrends.length);
  console.log('- Internal context available:', !!combinedInsights.internalStrategicContext);
  console.log('- Analytical questions generated:', combinedInsights.analyticalQuestions.length);
  console.log('- Total sources:', combinedInsights.researchSources.length);
  
  return combinedInsights;
}

/**
 * Run all examples
 */
export async function runAllExamples() {
  console.log('🔍 Enhanced Investigative Research Agent Examples\n');
  
  try {
    console.log('1. Internet Search Example:');
    await exampleInternetSearch();
    
    console.log('\n2. Document Analysis Example:');
    await exampleDocumentAnalysis();
    
    console.log('\n3. Question-Answer Analysis Example:');
    await exampleQuestionAnswerAnalysis();
    
    console.log('\n4. Combined Research Example:');
    await exampleCombinedResearch();
    
    console.log('\n5. Full Investigation Example:');
    await exampleFullInvestigation();
    
    console.log('\n✅ All examples completed successfully!');
    
  } catch (error) {
    console.error('❌ Example execution failed:', error);
  }
}

// Export individual examples for selective use
export {
  exampleInternetSearch,
  exampleDocumentAnalysis,
  exampleQuestionAnswerAnalysis,
  exampleFullInvestigation,
  exampleCombinedResearch
};
